// Add electron-squirrel-startup at the very top
// eslint-disable-next-line @typescript-eslint/no-var-requires
if (require('electron-squirrel-startup')) 
  process.exit(0);

import { app } from 'electron';
import { createLauncherWindow, showLauncherWindow } from './window';
import { launcher } from './launcher/launcher';
import { setupAutoUpdater } from './utils/autoUpdater';

// Setup app ready handler
app.whenReady().then(async () => {
  console.log('[LAUNCHER] App ready, initializing launcher...');

  // Setup auto-updater
  setupAutoUpdater();

  // Create and show the launcher window
  createLauncherWindow();
  showLauncherWindow();
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// Clean up handlers on app quit
app.on('before-quit', () => {
  console.log('[LAUNCHER] Cleaning up before quit...');
  launcher.cleanup();
});