import { useState, useEffect, useCallback } from 'react';
import { SetupState } from '@src/types/setup';
import { useSetupEvents } from './useSetupEvents';

/**
 * Hook for managing the setup process
 */
export const useSetup = () => {
  // Setup state
  const [state, setState] = useState<SetupState>({
    // Overall readiness
    launchReady: null,

    // Download state
    downloadNeeded: false,
    downloadReason: '',
    isDownloading: false,
    downloadProgress: null,
    downloadError: null,
    downloadComplete: false,

    // Backend download state
    backendDownloading: false,
    backendProgress: null,
    backendError: null,
    backendComplete: false,

    // Main app state
    mainAppError: null,

    // Overall state
    currentPhase: 'checking'
  });
  
  // Check launch readiness (both main app and backend)
  const checkLaunchReady = useCallback(async () => {
    try {
      if (!window.electron) {
        throw new Error('Electron API not available');
      }

      setState(prev => ({ ...prev, currentPhase: 'checking' }));

      const result = await window.electron.ipcRenderer.invoke('launcher:check-ready');

      setState(prev => ({
        ...prev,
        launchReady: result,
        downloadNeeded: result.mainAppNeeded || result.backendNeeded,
        downloadReason: result.reason,
        currentPhase: result.allReady ? 'starting' : 'downloading'
      }));

      return !result.allReady;
    } catch (error) {
      console.error('Failed to check launch readiness:', error);

      setState(prev => ({
        ...prev,
        downloadError: error instanceof Error ? error.message : String(error),
        currentPhase: 'error'
      }));

      return false;
    }
  }, []);
  
  // Start main app download
  const startMainAppDownload = useCallback(async () => {
    try {
      if (!window.electron) {
        throw new Error('Electron API not available');
      }

      setState(prev => ({
        ...prev,
        isDownloading: true,
        downloadError: null,
        currentPhase: 'downloading'
      }));

      const result = await window.electron.ipcRenderer.invoke('download:start');

      if (!result) {
        // Don't treat "already in progress" as an error - just log it
        console.log('[SETUP] Main app download already in progress or completed');
        return true; // Return success since download is happening
      }

      return true;
    } catch (error) {
      console.error('Failed to start main app download:', error);

      setState(prev => ({
        ...prev,
        isDownloading: false,
        downloadError: error instanceof Error ? error.message : String(error),
        currentPhase: 'error'
      }));

      return false;
    }
  }, []);

  // Start backend download
  const startBackendDownload = useCallback(async () => {
    try {
      if (!window.electron) {
        throw new Error('Electron API not available');
      }

      setState(prev => ({ ...prev, backendError: null }));

      const result = await window.electron.ipcRenderer.invoke('backend:download');

      // Don't treat "already in progress" as an error - just log it
      if (!result) {
        console.log('[SETUP] Backend download already in progress or completed');
      }
    } catch (error) {
      console.error('[SETUP] Backend download error:', error);
      setState(prev => ({
        ...prev,
        backendError: error instanceof Error ? error.message : 'Unknown error'
      }));
    }
  }, []);
  
  // Cancel main app download
  const cancelMainAppDownload = useCallback(async () => {
    try {
      if (!window.electron) {
        throw new Error('Electron API not available');
      }

      const result = await window.electron.ipcRenderer.invoke('download:cancel');

      if (result) {
        setState(prev => ({
          ...prev,
          isDownloading: false
        }));
      }

      return result;
    } catch (error) {
      console.error('Failed to cancel main app download:', error);
      return false;
    }
  }, []);

  // Cancel backend download
  const cancelBackendDownload = useCallback(async () => {
    try {
      if (!window.electron) {
        throw new Error('Electron API not available');
      }

      const result = await window.electron.ipcRenderer.invoke('backend:cancel');
      
      if (result) {
        setState(prev => ({
          ...prev,
          backendDownloading: false,
          backendProgress: null
        }));
      }
    } catch (error) {
      console.error('[SETUP] Cancel backend download error:', error);
    }
  }, []);
  
  // Launch main app
  const launchMainApp = useCallback(async () => {
    try {
      if (!window.electron) {
        throw new Error('Electron API not available');
      }
      
      setState(prev => ({
        ...prev,
        currentPhase: 'starting',
        mainAppError: null
      }));

      const result = await window.electron.ipcRenderer.invoke('launcher:launch-main-app');

      if (!result) {
        throw new Error('Failed to launch main app');
      }

      // After launching, wait for the main app to be ready
      setState(prev => ({
        ...prev,
        currentPhase: 'waiting'
      }));

      return true;
    } catch (error) {
      console.error('Failed to launch main app:', error);
      
      setState(prev => ({
        ...prev,
        mainAppError: error instanceof Error ? error.message : String(error),
        currentPhase: 'error'
      }));
      
      return false;
    }
  }, []);
  
  // Exit launcher
  const exitLauncher = useCallback(async () => {
    try {
      if (!window.electron) {
        throw new Error('Electron API not available');
      }
      
      await window.electron.ipcRenderer.invoke('launcher:exit');
    } catch (error) {
      console.error('Failed to exit launcher:', error);
    }
  }, []);
  
  // Set up event listeners
  useSetupEvents({ setState, launchMainApp });
  
  // Auto-start the setup process
  useEffect(() => {
    console.log('[SETUP] useEffect triggered - starting setup process');

    const startSetup = async () => {
      try {
        // Get environment variables from main process
        if (!window.electron) {
          console.error('[SETUP] Electron API not available');
          throw new Error('Electron API not available');
        }

        console.log('[SETUP] Getting environment variables...');
        const env = await window.electron.ipcRenderer.invoke('launcher:get-env');
        const isDev = env.NODE_ENV === 'development';
        const forceDownload = env.FORCE_DOWNLOAD === 'true';
        console.log('[SETUP] Environment check - isDev:', isDev, 'forceDownload:', forceDownload);
        console.log('[SETUP] Environment variables:', env);
        console.log('[SETUP] Raw NODE_ENV:', env.NODE_ENV);

        if (isDev && !forceDownload) {
          // In dev mode without force download, skip download check and launch directly
          console.log('[SETUP] Development mode - launching directly');
          await launchMainApp();
          return;
        }

        // Production mode or forced download mode - check for downloads first
        console.log('[SETUP] Production/forced download mode - checking launch readiness');

        // Get the readiness result directly from the IPC call
        if (!window.electron) {
          throw new Error('Electron API not available');
        }

        const readiness = await window.electron.ipcRenderer.invoke('launcher:check-ready');
        console.log('[SETUP] Readiness result:', readiness);

        // Update state with the readiness info
        setState(prev => ({
          ...prev,
          launchReady: readiness,
          downloadNeeded: readiness.mainAppNeeded || readiness.backendNeeded,
          downloadReason: readiness.reason,
          currentPhase: readiness.allReady ? 'starting' : 'downloading'
        }));

        const needsDownload = !readiness.allReady;
        console.log('[SETUP] Needs download:', needsDownload);

        if (needsDownload) {
          // Sequential downloads - main app first, then backend
          console.log('[SETUP] Starting sequential downloads - mainApp:', readiness.mainAppNeeded, 'backend:', readiness.backendNeeded);

          if (readiness.mainAppNeeded) {
            console.log('[SETUP] Step 1: Starting main app download');
            setState(prev => ({ ...prev, currentPhase: 'downloading' }));
            await startMainAppDownload();
            // Auto-launch effect will handle next step when main app download completes
          } else if (readiness.backendNeeded) {
            console.log('[SETUP] Step 1: Starting backend download (no main app needed)');
            setState(prev => ({ ...prev, currentPhase: 'downloading' }));
            await startBackendDownload();
            // Auto-launch effect will handle next step when backend download completes
          }
        } else {
          console.log('[SETUP] No downloads needed - launching main app');
          setState(prev => ({ ...prev, currentPhase: 'starting' }));
          await launchMainApp();
        }
      } catch (error) {
        console.error('[SETUP] Error in startSetup:', error);
      }
    };

    startSetup();
  }, []); // Empty dependency array - only run once on mount

  // Auto-progression: main app download → backend download → launch
  useEffect(() => {
    const checkProgression = async () => {
      if (!state.launchReady) return;

      const mainAppNeeded = state.launchReady.mainAppNeeded;
      const backendNeeded = state.launchReady.backendNeeded;
      const mainAppDone = !mainAppNeeded || state.downloadComplete;
      const backendDone = !backendNeeded || state.backendComplete;

      // Step 1: Main app download completed, start backend download if needed
      if (mainAppDone && backendNeeded && !state.backendComplete && !state.backendDownloading && state.currentPhase === 'downloading') {
        console.log('[SETUP] Step 2: Main app complete, starting backend download');
        await startBackendDownload();
      }

      // Step 2: All downloads complete, launch main app
      else if (mainAppDone && backendDone && state.currentPhase === 'downloading') {
        console.log('[SETUP] All downloads complete - launching main app');
        setState(prev => ({ ...prev, currentPhase: 'starting' }));
        await launchMainApp();
      }
    };

    checkProgression();
  }, [state.downloadComplete, state.backendComplete, state.backendDownloading, state.launchReady, state.currentPhase]);
  
  return {
    state,
    actions: {
      checkLaunchReady,
      startMainAppDownload,
      startBackendDownload,
      cancelMainAppDownload,
      cancelBackendDownload,
      launchMainApp,
      exitLauncher
    },
    backendDownloading: state.backendDownloading,
    backendProgress: state.backendProgress,
    backendError: state.backendError,
    backendComplete: state.backendComplete,
  };
};
