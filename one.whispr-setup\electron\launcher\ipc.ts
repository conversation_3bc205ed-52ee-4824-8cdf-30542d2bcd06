import { app, ipcMain } from 'electron';
import { closeLauncherWindow } from '../window';

/**
 * Setup IPC handlers for launcher operations
 */
export function setupLauncherIpc(launcher: any): void {
  // Remove existing handlers first to prevent duplicates during hot reload
  ipcMain.removeHandler('launcher:check-ready');
  ipcMain.removeHandler('launcher:launch-main-app');
  ipcMain.removeHandler('launcher:exit');

  // Check if everything is ready to launch
  ipcMain.handle('launcher:check-ready', async () => {
    return launcher.checkLaunchReady();
  });

  // Launch main app
  ipcMain.handle('launcher:launch-main-app', async () => {
    return launcher.launchMainApp();
  });

  // Get environment variables
  ipcMain.handle('launcher:get-env', async () => {
    return {
      NODE_ENV: process.env.NODE_ENV,
      FORCE_DOWNLOAD: process.env.FORCE_DOWNLOAD,
      DOWNLOAD_PATH: process.env.DOWNLOAD_PATH
    };
  });

  // Exit launcher
  ipcMain.handle('launcher:exit', () => {
    console.log('[LAUNCHER] Exit requested via IPC');

    // Close the launcher window properly (sets isQuitting flag)
    closeLauncherWindow();

    // Force quit after a moment if normal quit doesn't work
    setTimeout(() => {
      app.quit();
      setTimeout(() => {
        process.exit(0);
      }, 1000);
    }, 500);

    return true;
  });

  console.log('[LAUNCHER] IPC handlers registered');
}
