{"appId": "one.whispr-app", "productName": "One Whispr", "asar": true, "directories": {"output": ".release-microsoft"}, "files": [".dist/main/**/*", ".dist/renderer/**/*", "node_modules/**/*", "!node_modules/.cache/**/*", "!python/utils/**/*"], "asarUnpack": ["node_modules/**/*.node"], "extraResources": [{"from": "python/models", "to": "models", "filter": ["**/*"]}, {"from": ".dist/One Whispr Backend", "to": "backend", "filter": ["**/*"]}, {"from": "src/assets/one.whispr-white.png", "to": "one.whispr-white.png"}, {"from": "src/assets/one.whispr-black.png", "to": "one.whispr-black.png"}, {"from": "src/assets/sounds", "to": "sounds"}], "win": {"target": [{"target": "appx", "arch": ["x64"]}], "icon": "src/assets/icon.ico"}, "appx": {"displayName": "One Whispr", "publisherDisplayName": "One Whispr Team", "identityName": "IjazSadiqBasha.OneWhispr", "publisher": "CN=IjazSadiqBasha", "applicationId": "OneWhispr", "backgroundColor": "#1a1a1a", "showNameOnTiles": true, "languages": ["en-US"], "setBuildNumber": true}}