import * as fs from 'fs-extra';
import * as path from 'path';
import { getLauncherWindow } from '../../window';
import { downloader } from '../main-app/mainAppDownloader';
import { setupBackendIpc } from './ipc';
import { VersionManager } from './versionManager';
import { LocalExtractor } from './localExtractor';
import { ComponentDownloaders } from './componentDownloaders';

/**
 * Backend downloader orchestrator that handles separated builds
 * - Base Runtime: ~1.3GB (downloaded once, rarely updated)
 * - Scripts Code: ~200KB (frequent updates)
 */
export class BackendDownloader {
  private backendPath: string;
  private isDownloading: boolean = false;
  private abortController: AbortController | null = null;
  private versionManager: VersionManager;
  private localExtractor: LocalExtractor;
  private componentDownloaders: ComponentDownloaders;
  
  constructor() {
    // In development mode, extract directly to the built app's backend directory
    // In production mode, extract to the downloaded app's backend directory
    const { app } = require('electron');
    const forceDownload = process.env.FORCE_DOWNLOAD === 'true';
    const isDev = !app.isPackaged && !forceDownload;

    if (isDev) {
      // Development: extract to the built app's backend directory
      const builtAppPath = path.join(process.cwd(), '..', 'one.whispr-app', '.release-direct', 'win-unpacked');
      this.backendPath = path.join(builtAppPath, 'resources', 'backend');
    } else {
      // Production: extract to the downloaded app's backend directory
      const mainAppPath = downloader.getDownloadPath();
      this.backendPath = path.join(mainAppPath, 'resources', 'backend');
    }

    console.log(`[BACKEND] Backend extraction target: ${this.backendPath}`);

    // Create backend directory if it doesn't exist
    fs.ensureDirSync(this.backendPath);

    // Initialize components
    this.versionManager = new VersionManager(this.backendPath);
    this.localExtractor = new LocalExtractor(this.backendPath);
    this.componentDownloaders = new ComponentDownloaders(this.backendPath);
    setupBackendIpc(this);
  }

  /**
   * Check if backend update is needed
   */
  async checkBackendUpdate(): Promise<{
    runtimeNeeded: boolean,
    scriptsNeeded: boolean,
    runtimeVersion?: string,
    scriptsVersion?: string,
    reason: string
  }> {
    // First, check if we have local 7z files (but don't extract them yet)
    const hasLocal7zFiles = this.versionManager.checkForLocal7zFiles();
    if (hasLocal7zFiles.hasRuntimeZip || hasLocal7zFiles.hasScriptsZip) {
      return {
        runtimeNeeded: hasLocal7zFiles.hasRuntimeZip,
        scriptsNeeded: hasLocal7zFiles.hasScriptsZip,
        runtimeVersion: '1.0.0', // Will be read after extraction
        scriptsVersion: '1.0.0', // Will be read after extraction
        reason: 'First time installation - downloading base runtime and scripts'
      };
    }

    // If no local files, delegate to version manager
    return this.versionManager.checkBackendUpdate();
  }

  /**
   * Download backend components (runtime and/or scripts)
   */
  async downloadBackend(): Promise<boolean> {
    console.log('[BACKEND] downloadBackend() called');

    if (this.isDownloading) {
      console.log('[BACKEND] Download already in progress');
      // Return true since the download/extraction is happening, just not started by this call
      return true;
    }

    try {
      console.log('[BACKEND] Starting download process...');
      this.isDownloading = true;
      this.abortController = new AbortController();
      this.componentDownloaders.setAbortController(this.abortController);

      // Check what needs to be downloaded
      console.log('[BACKEND] Checking what needs to be downloaded...');

      // First try to extract local 7z files
      const localExtractionResult = await this.localExtractor.checkAndExtractLocal7zFiles();
      if (localExtractionResult.extracted) {
        console.log('[BACKEND] Local 7z files extracted successfully');

        // Notify completion
        const launcherWindow = getLauncherWindow();
        console.log('[BACKEND] Launcher window available:', !!launcherWindow);
        if (launcherWindow) {
          console.log('[BACKEND] Sending backend:complete event to renderer');
          launcherWindow.webContents.send('backend:complete');
        } else {
          console.warn('[BACKEND] No launcher window available to send backend:complete event');
        }

        this.isDownloading = false;
        this.abortController = null;
        return true;
      }

      // If no local files, check for online updates
      const updateCheck = await this.checkBackendUpdate();
      console.log('[BACKEND] Update check result:', updateCheck);

      if (!updateCheck.runtimeNeeded && !updateCheck.scriptsNeeded) {
        console.log('[BACKEND] No updates needed');
        this.isDownloading = false;
        this.abortController = null;
        return true;
      }

      // Download base runtime if needed
      if (updateCheck.runtimeNeeded) {
        await this.componentDownloaders.downloadRuntime();
      }

      // Download scripts (always download to ensure latest)
      if (updateCheck.scriptsNeeded || updateCheck.runtimeNeeded) {
        await this.componentDownloaders.downloadScripts();
      }

      // Download Whisper base model as final step
      await this.componentDownloaders.downloadWhisperBaseModel();

      this.isDownloading = false;
      this.abortController = null;
      
      // Notify completion
      const launcherWindow = getLauncherWindow();
      if (launcherWindow) {
        launcherWindow.webContents.send('backend:complete');
      }
      
      return true;
    } catch (error) {
      console.error('[BACKEND] Download error:', error);
      
      this.isDownloading = false;
      this.abortController = null;
      
      // Notify error
      const launcherWindow = getLauncherWindow();
      if (launcherWindow) {
        launcherWindow.webContents.send('backend:error', {
          message: error instanceof Error ? error.message : String(error)
        });
      }
      
      return false;
    }
  }

  /**
   * Cancel the current download
   */
  cancelDownload(): boolean {
    if (!this.isDownloading || !this.abortController) {
      return false;
    }

    console.log('[BACKEND] Cancelling download');

    this.abortController.abort();
    this.isDownloading = false;
    this.abortController = null;

    return true;
  }

  /**
   * Get the backend installation path
   */
  getBackendPath(): string {
    return this.backendPath;
  }

  /**
   * Check if backend is ready to use
   */
  async isBackendReady(): Promise<boolean> {
    return this.versionManager.isBackendReady();
  }
}

// Export singleton instance
export const backendDownloader = new BackendDownloader();
